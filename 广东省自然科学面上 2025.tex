%!TEX program = xelatex

% 编译顺序: xelatex -> bibtex -> xelatex -> xelatex
% 国家自然科学基金NSFC面上项目申请书正文模板（2023年版）version1.0
% 声明：
% 注意！！！非国家自然科学基金委官方模版！！！由个人根据官方MsWord模版制作。本模版的作者尽力使本模版和官方模版生成的PDF文件视觉效果大致一样，然而，并不保证本模版有用，也不对使用本模版造成的任何直接或间接后果负责。 不得将本模版用于商用或获取经济利益。本模版可以自由修改以满足用户自己的需要。但是如果要传播本模版，则只能传播未经修改的版本。使用本模版意味着同意上述声明。
% 强烈建议自己对照官方MsWord模板确认格式和文字是否一致，尤其是蓝字部分。
% 如有问题，可以发邮件询问原作者 <EMAIL>，或本版本的修改者 <EMAIL>


\documentclass[12pt,UTF8,AutoFakeBold=3,a4paper]{ctexart} %默认小四号字。允许楷体粗体。
\usepackage[english]{babel} %支持混合语言
\usepackage[dvipsnames]{xcolor}
\usepackage{xeCJK}
\setCJKmainfont{SimSun}

\usepackage{xeCJKfntef}

\usepackage[backend=biber,style=phys]{biblatex}
\addbibresource{myexample.bib}

\usepackage{pifont}
\usepackage{graphicx}
\usepackage{ulem}
% \usepackage{CJKulem}
\usepackage{setspace}
\usepackage{enumitem}
\usepackage{amsmath} %更多数学符号
\usepackage{mathtools}
\usepackage{wasysym}
\usepackage[unicode]{hyperref} %提供跳转链接
\usepackage{geometry} %改改尺寸
\usepackage{amsfonts}

\usepackage{tikz}
\usepackage{bibentry}
\nobibliography*
% \usepackage[numbers,sort&compress]{natbib}
% \setlength{\bibsep}{0.0ex} % 缩小参考文献间的垂直间距


%latex的页边距比word的视觉效果要大一些，稍微调整一下
\geometry{left=3.20cm,right=3.27cm,top=2.67cm,bottom=3.27cm}
\pagestyle{empty}
\setcounter{secnumdepth}{-2} %不让那些section和subsection自带标号，标号格式自己掌握
\definecolor{MsBlue}{RGB}{0,112,192} %Ms Word 的蓝色和latex xcolor包预定义的蓝色不一样。通过屏幕取色得到。
\definecolor{BoldBlue}{RGB}{0,0,64} % Color(0, 0, 64)
% Renaming floats with babel
\addto\captionsenglish{
    \renewcommand{\contentsname}{目录}
    \renewcommand{\listfigurename}{插图目录}
    \renewcommand{\listtablename}{表格}
    % \renewcommand{\refname}{\sihao \kaishu 参考文献}
    \renewcommand{\refname}{\sihao \kaishu \leftline{参考文献}} %这几个字默认字号稍大，改成四号字，楷书，居左(默认居中) 根据喜好自行修改，官方模板未作要求
    \renewcommand{\abstractname}{摘要}
    \renewcommand{\indexname}{索引}
    \renewcommand{\tablename}{表}
    \renewcommand{\figurename}{\kaishu 图}
    } %把Figure改成‘图’，reference改成‘参考文献’。如此处理是为了避免和babel包冲突。
%定义字号
\newcommand{\chuhao}{\fontsize{42pt}{\baselineskip}\selectfont}
\newcommand{\xiaochuhao}{\fontsize{36pt}{\baselineskip}\selectfont}
\newcommand{\yihao}{\fontsize{26pt}{\baselineskip}\selectfont}
\newcommand{\erhao}{\fontsize{22pt}{\baselineskip}\selectfont}
\newcommand{\xiaoerhao}{\fontsize{18pt}{\baselineskip}\selectfont}
\newcommand{\sanhao}{\fontsize{16pt}{\baselineskip}\selectfont}
\newcommand{\sihao}{\fontsize{14pt}{\baselineskip}\selectfont}
\newcommand{\xiaosihao}{\fontsize{12pt}{\baselineskip}\selectfont}
\newcommand{\wuhao}{\fontsize{10.5pt}{\baselineskip}\selectfont}
\newcommand{\xiaowuhao}{\fontsize{9pt}{\baselineskip}\selectfont}
\newcommand{\liuhao}{\fontsize{7.875pt}{\baselineskip}\selectfont}
\newcommand{\qihao}{\fontsize{5.25pt}{\baselineskip}\selectfont}
%字号对照表
%二号 21pt
%四号 14
%小四 12
%五号 10.5
%设置行距 1.5倍
\renewcommand{\baselinestretch}{2}
\XeTeXlinebreaklocale "zh"           % 中文断行
% \usepackage{tikz}
% \newcommand*\circled[1]{\tikz[baseline=(char.base)]{
%             \node[shape=circle,draw,inner sep=2pt] (char) {#1};}}
%%%% 正文开始 %%%%
\begin{document}
\begin{center}
{\xiaoerhao \bfseries 广东省自然科学基金面上项目}

\vspace{0.5em}
{\xiaoerhao \bfseries 报告正文}
\end{center}



{\sihao \bfseries 参照以下提纲撰写，要求内容翔实、清晰，层次分明，标题突出。}

\begin{refsection}
    

{\sihao {\bfseries 一、立论依据}}

{\sihao {\bfseries 1、研究意义}（对基础研究，着重结合国际科学发展趋势，论述项目的科学意义；对应用基础研究，着重结合学科前沿、围绕国民经济和社会发展中的重要科技问题，论述其应用前景）。}

% 1. 研究意义
\input{contents/1-1-research-significance.tex}



{\sihao {\bfseries 2、国内外研究现状。}}
% 2. 国内外研究现状及发展动态分析
\input{contents/1-2-literature-review.tex}

{\sihao {\bfseries 3、主要参考文献及出处。}（格式：论文--作者．题目．刊名．年份．卷(期)．页码／专著--作者．书名．出版者．年份）。}


\begin{spacing}{1.1}
{\kaishu \wuhao
% \bibliographystyle{utphys2}
% \bibliography{myexample}

\printbibliography[title={~}, heading=subbibliography]
}
\end{spacing}
\end{refsection}



\newpage

\vspace{2em}




{\sihao {\bfseries 二、研究方案}}



{\sihao {\bfseries 1、研究目标、研究内容和拟解决的关键问题。}}
% 2.1 研究目标
\input{contents/2-1-research-goal.tex}
% 2.2 研究内容
\input{contents/2-2-content.tex}
% 2.3 拟解决的关键科学问题
\input{contents/2-3-key.tex}







\vspace{1em}
{\sihao {\bfseries 2、拟采取的研究方法、技术路线、实验方案及可行性分析。}}
% 拟采取的技术路线
\input{contents/3-1-tech.tex}
% 可行性分析
\input{contents/3-2-feasibility.tex}




\vspace{1em}
{\sihao {\bfseries 3、本项目的创新之处。}}
\input{contents/4-feature.tex}

\vspace{1em}
{\sihao {\bfseries 4、年度研究计划及预期研究成果。}}
% 5.1 年度研究计划
\input{contents/5-1-plan.tex}
% 5.2 预期研究成果
\input{contents/5-2-achievement.tex}
% 5.3 拟组织的学术交流活动及国际合作交流计划
\input{contents/5-3-activity.tex}


\vspace{2em}
{\sihao {\bfseries 三、研究条件与基础}}


{\sihao {\bfseries 1、已取得的研究工作成绩及与本项目有关的研究工作积累}（请着重填写申请人近期的主要工作业绩以及与本项目相关的研究工作积累）。}
% 1. 研究基础
\input{contents/6-fundation.tex}


{\sihao {\bfseries 2、已具备的实验条件，尚缺少的实验条件和拟解决的途径}（包括利用国家重点实验室和部门开放实验室的计划与落实情况）。}
% 2. 工作条件
\input{contents/7-condition.tex}





\end{document}


